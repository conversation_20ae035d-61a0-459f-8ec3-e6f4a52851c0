// Simple Playwright server launcher using browserType.launchServer()
// Config via env:
//   BROWSER: chromium | firefox | webkit (default: chromium)
//   SERVER_HOST: host to bind (default: 0.0.0.0)
//   SERVER_PORT: port to listen (default: 3000)
//   HEADLESS: true|false (default: true)

const { chromium, firefox, webkit } = require('playwright');

(async () => {
  const browserName = process.env.BROWSER || 'chromium';
  const host = process.env.SERVER_HOST || '0.0.0.0';
  const port = Number(process.env.SERVER_PORT || 3000);
  const headless = String(process.env.HEADLESS || 'true').toLowerCase() !== 'false';

  const map = { chromium, firefox, webkit };
  const browserType = map[browserName];
  if (!browserType) {
    console.error(`[pw-server] Unknown BROWSER: ${browserName}. Use chromium|firefox|webkit.`);
    process.exit(1);
  }

  try {
    console.log(`[pw-server] Launching ${browserName} server on ${host}:${port} (headless=${headless})`);
    const server = await browserType.launchServer({ host, port, headless });
    const wsEndpoint = server.wsEndpoint();
    console.log(`[pw-server] Ready. WS endpoint: ${wsEndpoint}`);

    const shutdown = async (signal) => {
      console.log(`[pw-server] Received ${signal}. Closing...`);
      try { await server.close(); } catch (e) { /* ignore */ }
      process.exit(0);
    };
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  } catch (err) {
    console.error('[pw-server] Failed to launch server:', err);
    process.exit(1);
  }
})();
