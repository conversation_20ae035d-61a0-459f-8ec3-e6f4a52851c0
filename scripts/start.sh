#!/usr/bin/env bash
set -euo pipefail

# Env defaults
export DISPLAY=${DISPLAY:-:99}
export SCREEN_RESOLUTION=${SCREEN_RESOLUTION:-1280x800x24}
export VNC_PORT=${VNC_PORT:-5900}
export NOVNC_PORT=${NOVNC_PORT:-6080}
export SERVER_PORT=${SERVER_PORT:-3000}
export SERVER_HOST=${SERVER_HOST:-0.0.0.0}
export PW_VERSION=${PW_VERSION:-1.55.0}

# Start X virtual framebuffer
if ! pgrep -x Xvfb >/dev/null 2>&1; then
  echo "[start] Launching Xvfb on $DISPLAY ($SCREEN_RESOLUTION)"
  Xvfb "$DISPLAY" -screen 0 "$SCREEN_RESOLUTION" -nolisten tcp -ac +extension RANDR &
fi

# Small window manager (optional but improves VNC UX)
if command -v openbox >/dev/null 2>&1; then
  echo "[start] Launching openbox"
  openbox >/dev/null 2>&1 &
elif command -v fluxbox >/dev/null 2>&1; then
  echo "[start] Launching fluxbox"
  fluxbox >/dev/null 2>&1 &
fi

# Wait for X display to become ready
echo "[start] Waiting for X display $DISPLAY to be ready..."
for i in $(seq 1 60); do
  if command -v xdpyinfo >/dev/null 2>&1 && xdpyinfo -display "$DISPLAY" >/dev/null 2>&1; then
    echo "[start] X display is ready (after ${i}s)"
    break
  fi
  # Fallback: check X socket existence
  if [ -S "/tmp/.X11-unix/X${DISPLAY#:}" ]; then
    echo "[start] X socket present; proceeding"
    break
  fi
  sleep 1
  if [ "$i" -eq 60 ]; then
    echo "[warn] X display not ready after 60s, continuing anyway"
  fi
done

# Start x11vnc attached to the DISPLAY
if ! pgrep -x x11vnc >/dev/null 2>&1; then
  echo "[start] Launching x11vnc on :$VNC_PORT"
  x11vnc -display "$DISPLAY" -forever -shared -rfbport "$VNC_PORT" -nopw -quiet -xkb &
fi

# Start noVNC via websockify
if ! pgrep -f "websockify .* $NOVNC_PORT" >/dev/null 2>&1; then
  NOVNC_WEB_ROOT="/usr/share/novnc"
  if [ ! -d "$NOVNC_WEB_ROOT" ]; then
    # Fallback path for some distros
    NOVNC_WEB_ROOT="/usr/share/novnc/"
  fi
  echo "[start] Launching noVNC on http://0.0.0.0:$NOVNC_PORT"
  websockify --web "$NOVNC_WEB_ROOT" "$NOVNC_PORT" 127.0.0.1:"$VNC_PORT" >/dev/null 2>&1 &
fi

# Start Playwright remote server via Node
echo "[start] Launching Playwright server (Node) on $SERVER_HOST:$SERVER_PORT (v$PW_VERSION)"
exec node /home/<USER>/pw-server.js
