from playwright.sync_api import sync_playwright
import os
import time
import re
import subprocess
from typing import Optional
from datetime import datetime

"""
If PW_WS is provided, connect directly to it.
Otherwise, read docker logs from CONTAINER_NAME to extract the full WS endpoint
printed by pw-server.js (e.g., "WS endpoint: ws://0.0.0.0:3001/<token>").
"""
WS_ENDPOINT = os.environ.get("PW_WS", "")
CONTAINER_NAME = os.environ.get("CONTAINER_NAME", "pw1")
LOG_TAIL = int(os.environ.get("LOG_TAIL", "2000"))
WAIT_LOG_SECONDS = int(os.environ.get("WAIT_LOG_SECONDS", "15"))

URL = os.environ.get("TARGET_URL", "https://www.google.com")
STAY_MS = int(os.environ.get("STAY_MS", "10000"))
SCREENSHOT_PATH = os.environ.get("SCREENSHOT_PATH", "screenshots")

WS_REGEX = re.compile(r"WS endpoint:\s*(ws://[^\s]+)")


def get_ws_endpoint_from_logs(container: str, tail: int) -> Optional[str]:
    try:
        out = subprocess.check_output(
            ["docker", "logs", "--tail", str(tail), container],
            stderr=subprocess.STDOUT,
            text=True,
        )
    except subprocess.CalledProcessError as e:
        print(f"[client] docker logs error: {e.output}")
        return None
    for line in out.splitlines()[::-1]:
        m = WS_REGEX.search(line)
        if m:
            return m.group(1)
    return None


def wait_for_ws_endpoint(container: str, tail: int, timeout_sec: int) -> Optional[str]:
    print(
        f"[client] Waiting for WS endpoint from container '{container}' logs (<= {timeout_sec}s)..."
    )
    deadline = time.time() + timeout_sec
    while time.time() < deadline:
        ws = get_ws_endpoint_from_logs(container, tail)
        if ws:
            print(f"[client] Found WS endpoint: {ws}")
            return ws
        time.sleep(1)
    return None


def main() -> None:
    # Create screenshots directory if it doesn't exist
    os.makedirs(SCREENSHOT_PATH, exist_ok=True)

    # Resolve WS endpoint
    ws = WS_ENDPOINT.strip()
    if not ws:
        ws = wait_for_ws_endpoint(CONTAINER_NAME, LOG_TAIL, WAIT_LOG_SECONDS)
        if not ws:
            raise RuntimeError(
                f"Could not find WS endpoint in docker logs for '{CONTAINER_NAME}'. "
                f"Set PW_WS explicitly or ensure container prints the endpoint."
            )

    print(f"[client] Connecting to {ws}")
    with sync_playwright() as p:
        # Try all browser types to be robust to server type
        browser = None
        last_err = None
        for bt in (p.chromium, p.firefox, p.webkit):
            try:
                browser = bt.connect(ws_endpoint=ws)
                break
            except Exception as e:
                last_err = e
        if not browser:
            raise RuntimeError(f"Failed to connect to WS endpoint: {last_err}")
        try:
            context = browser.new_context()
            page = context.new_page()
            print(f"[client] Goto {URL}")
            page.goto(URL, wait_until="domcontentloaded")

            # Wait a bit for the page to fully load
            page.wait_for_timeout(2000)

            # Take screenshot
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_file = os.path.join(SCREENSHOT_PATH, f"google_{timestamp}.png")
            page.screenshot(path=screenshot_file, full_page=True)
            print(f"[client] Screenshot saved to: {screenshot_file}")

            print(f"[client] Stay for {STAY_MS} ms")
            page.wait_for_timeout(STAY_MS)
        finally:
            print("[client] Closing browser")
            browser.close()


if __name__ == "__main__":
    main()
